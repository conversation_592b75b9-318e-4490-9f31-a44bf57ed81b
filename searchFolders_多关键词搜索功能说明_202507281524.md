# searchFolders 多关键词搜索功能说明

**更新日期**: 2025-07-28 15:24

## 功能概述

`searchFolders` 方法已升级支持多关键词模糊搜索功能，用户可以传入多个关键词进行更精确的文件夹检索。

## 主要改进

### 1. 多关键词支持

- **原功能**: 只支持单个关键词搜索
- **新功能**: 支持多个关键词同时搜索，使用 AND 逻辑连接

### 2. 灵活的分隔符支持

支持以下分隔符来分离多个关键词：

- **空格**: `"项目 文档"`
- **逗号**: `"技术,资料"`
- **分号**: `"会议;记录"`
- **混合使用**: `"技术, 文档; 分享"`

### 3. 智能关键词解析

- 自动去除空白字符
- 忽略空的关键词
- 支持连续的分隔符

### 4. 🆕 中英文混合关键词自动拆分

- **智能识别**: 自动识别中文字符和英文字符的边界
- **自动拆分**: 将混合关键词拆分为独立的中英文关键词
- **示例拆分**:
  - `"大模型NLP概念"` → `["大模型", "NLP", "概念"]`
  - `"AI人工智能研究"` → `["AI", "人工智能研究"]`
  - `"机器学习ML算法"` → `["机器学习", "ML", "算法"]`
  - `"深度学习DeepLearning"` → `["深度学习", "DeepLearning"]`

## 使用示例

### 单关键词搜索（向后兼容）

```cpp
QString result = databaseApi->searchFolders("项目");
// 返回包含"项目"的所有文件夹
```

### 多关键词搜索

```cpp
// 使用空格分隔
QString result = databaseApi->searchFolders("项目 文档");
// 返回同时包含"项目"和"文档"的文件夹

// 使用逗号分隔
QString result = databaseApi->searchFolders("技术,资料");
// 返回同时包含"技术"和"资料"的文件夹

// 混合分隔符
QString result = databaseApi->searchFolders("技术, 文档; 分享");
// 返回同时包含"技术"、"文档"和"分享"的文件夹
```

### 🆕 中英文混合关键词搜索

```cpp
// 自动拆分中英文混合关键词
QString result = databaseApi->searchFolders("大模型NLP概念");
// 自动拆分为: ["大模型", "NLP", "概念"]
// 返回同时包含这三个关键词的文件夹

// 复杂的中英文混合
QString result = databaseApi->searchFolders("AI人工智能研究");
// 自动拆分为: ["AI", "人工智能研究"]

// 多个中英文混合关键词
QString result = databaseApi->searchFolders("机器学习ML,深度学习DeepLearning");
// 自动拆分为: ["机器学习", "ML", "深度学习", "DeepLearning"]

// 包含数字的英文
QString result = databaseApi->searchFolders("Python3编程语言");
// 自动拆分为: ["Python3", "编程语言"]
```

## 技术实现

### 🆕 中英文混合关键词拆分算法

```cpp
// 新的智能关键词解析方法
QStringList keywords = splitChineseEnglishKeywords(keyword);

// 拆分算法实现
QStringList DatabaseApi::splitChineseEnglishKeywords(const QString &text)
{
    QStringList result;

    // 1. 首先按照传统分隔符拆分（空格、逗号、分号）
    QStringList initialSplit = text.split(QRegularExpression("[\\s,;]+"), Qt::SkipEmptyParts);

    // 2. 对每个片段进行中英文拆分
    for (const QString &segment : initialSplit)
    {
        // 使用正则表达式匹配连续的中文字符或连续的英文数字字符
        QRegularExpression chineseEnglishPattern(R"(([\u4e00-\u9fff]+|[a-zA-Z0-9]+))");
        QRegularExpressionMatchIterator iterator = chineseEnglishPattern.globalMatch(segment);

        while (iterator.hasNext())
        {
            QRegularExpressionMatch match = iterator.next();
            QString matchedText = match.captured(1);
            if (!matchedText.isEmpty())
            {
                result.append(matchedText);
            }
        }
    }

    // 3. 去重并保持顺序
    QStringList uniqueResult;
    for (const QString &keyword : result)
    {
        if (!uniqueResult.contains(keyword, Qt::CaseInsensitive))
        {
            uniqueResult.append(keyword);
        }
    }

    return uniqueResult;
}
```

### 正则表达式说明

- **中文字符匹配**: `[\u4e00-\u9fff]+` - 匹配连续的中文字符
- **英文数字匹配**: `[a-zA-Z0-9]+` - 匹配连续的英文字母和数字
- **组合模式**: `([\u4e00-\u9fff]+|[a-zA-Z0-9]+)` - 匹配中文或英文数字序列

### SQL查询构建

```sql
SELECT f.*,
       pf.name as parent_name,
       (SELECT COUNT(*) FROM folder_document_rel fdr WHERE fdr.folder_id = f.id) as document_count
FROM folders f
LEFT JOIN folders pf ON f.parent_id = pf.id
WHERE f.name LIKE ? AND f.name LIKE ? AND f.name LIKE ?
ORDER BY f.name ASC
```

### 参数绑定

- 每个关键词都会被包装为 `%keyword%` 格式
- 使用参数化查询防止SQL注入
- 支持动态数量的关键词

## 返回格式

返回的JSON格式保持不变：

```json
{
  "success": true,
  "data": [
    {
      "id": "folder_id",
      "name": "文件夹名称",
      "parent_id": "parent_folder_id",
      "parent_name": "父文件夹名称",
      "document_count": 5
    }
  ],
  "message": "搜索完成，找到 1 个匹配的文件夹",
  "count": 1
}
```

## 搜索逻辑

### AND 逻辑

- 所有关键词必须同时匹配才会返回结果
- 例如: `"项目 文档"` 只会返回同时包含"项目"和"文档"的文件夹

### 大小写不敏感

- 搜索默认不区分大小写
- 例如: `"Project"` 可以匹配 `"project"` 或 `"PROJECT"`

### 模糊匹配

- 使用 LIKE 操作符进行模糊匹配
- 关键词可以出现在文件夹名称的任何位置

## 错误处理

### 空关键词检查

```json
{
  "success": false,
  "message": "搜索关键字不能为空"
}
```

### SQL错误处理

```json
{
  "success": false,
  "error": "SQL错误详情",
  "message": "搜索文件夹失败: SQL错误详情"
}
```

## 性能考虑

1. **索引优化**: 建议在 `folders.name` 字段上创建索引
2. **查询限制**: 可以考虑添加 LIMIT 子句限制返回结果数量
3. **关键词数量**: 建议限制最大关键词数量以避免性能问题

## 测试用例

已创建完整的测试套件，包含以下测试：

### 基础功能测试 (`test_search_folders_202507281524.cpp`)

- ✅ 单关键词搜索
- ✅ 多关键词搜索
- ✅ 空关键词处理
- ✅ 不同分隔符支持
- ✅ 混合分隔符处理
- ✅ 大小写不敏感搜索

### 🆕 中英文拆分功能测试 (`test_chinese_english_split_202507281524.cpp`)

- ✅ 基本中英文混合拆分 (`"大模型NLP概念"`)
- ✅ 复杂混合关键词 (`"AI人工智能研究"`)
- ✅ 多个混合关键词 (`"机器学习ML,深度学习DeepLearning"`)
- ✅ 包含数字的关键词 (`"Python3编程语言"`)
- ✅ 特殊字符处理 (`"C++编程"`)
- ✅ 重复关键词去重
- ✅ 真实世界应用场景测试

### 演示程序 (`test_split_demo_202507281524.cpp`)

提供独立的拆分功能演示，包含详细的测试用例和输出结果。

#### 拆分测试示例

| 输入                         | 期望输出                               | 说明               |
| ---------------------------- | -------------------------------------- | ------------------ |
| `"大模型NLP概念"`            | `["大模型", "NLP", "概念"]`            | 基本中英文混合     |
| `"AI人工智能研究"`           | `["AI", "人工智能研究"]`               | 英文开头的混合     |
| `"机器学习ML算法"`           | `["机器学习", "ML", "算法"]`           | 中文-英文-中文模式 |
| `"深度学习DeepLearning框架"` | `["深度学习", "DeepLearning", "框架"]` | 包含长英文单词     |
| `"Python3编程语言"`          | `["Python3", "编程语言"]`              | 包含数字的英文     |

## 向后兼容性

- ✅ 完全向后兼容现有的单关键词搜索
- ✅ API接口保持不变
- ✅ 返回格式保持不变

## 使用建议

1. **用户界面**: 可以在搜索框中提示用户支持多关键词搜索和中英文混合搜索
2. **搜索提示**: 显示支持的分隔符类型和中英文自动拆分功能
3. **结果展示**: 可以高亮显示匹配的关键词，包括拆分后的中英文关键词
4. **搜索历史**: 保存用户的多关键词搜索历史
5. **🆕 拆分预览**: 在搜索框下方显示关键词拆分结果，让用户了解实际搜索的关键词

## 后续优化方向

### 基础功能优化

1. **OR逻辑支持**: 添加选项支持OR逻辑搜索
2. **权重排序**: 根据匹配关键词数量进行结果排序
3. **搜索建议**: 提供搜索关键词自动补全
4. **高级搜索**: 支持正则表达式或通配符搜索

### 🆕 中英文拆分功能优化

5. **智能分词**: 集成中文分词库（如jieba）进行更精确的中文分词
6. **英文词根识别**: 支持英文词根和变形识别（如search, searching, searched）
7. **专业术语识别**: 建立专业术语词典，避免错误拆分技术术语
8. **用户自定义规则**: 允许用户自定义拆分规则和词典
9. **拆分结果缓存**: 缓存常用关键词的拆分结果以提高性能
10. **多语言支持**: 扩展支持其他语言（日文、韩文等）的混合拆分
