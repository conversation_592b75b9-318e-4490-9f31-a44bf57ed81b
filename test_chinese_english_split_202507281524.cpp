#include <QtTest/QtTest>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QStandardPaths>
#include <QDir>
#include "qt-src/databaseapi.h"

class TestChineseEnglishSplit : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();
    
    void testBasicChineseEnglishSplit();
    void testComplexMixedKeywords();
    void testMultipleMixedKeywords();
    void testNumbersInKeywords();
    void testSpecialCharacters();
    void testEmptyAndWhitespace();
    void testDuplicateKeywords();
    void testRealWorldExamples();

private:
    DatabaseApi *m_databaseApi;
    QSqlDatabase m_db;
    
    void createTestFolders();
    void clearTestData();
};

void TestChineseEnglishSplit::initTestCase()
{
    // 设置测试数据库
    QString testDbPath = QStandardPaths::writableLocation(QStandardPaths::TempLocation) + "/test_chinese_english.db";
    QFile::remove(testDbPath);
    
    m_db = QSqlDatabase::addDatabase("QSQLITE", "test_chinese_english");
    m_db.setDatabaseName(testDbPath);
    
    if (!m_db.open()) {
        QFAIL("无法打开测试数据库");
    }
    
    m_databaseApi = new DatabaseApi(this);
    m_databaseApi->setDatabasePath(testDbPath);
    m_databaseApi->initializeDatabase();
}

void TestChineseEnglishSplit::cleanupTestCase()
{
    delete m_databaseApi;
    m_db.close();
    QSqlDatabase::removeDatabase("test_chinese_english");
}

void TestChineseEnglishSplit::init()
{
    createTestFolders();
}

void TestChineseEnglishSplit::cleanup()
{
    clearTestData();
}

void TestChineseEnglishSplit::createTestFolders()
{
    QSqlQuery query(m_db);
    
    // 创建包含中英文混合的测试文件夹
    QStringList folderNames = {
        "大模型NLP概念",
        "AI人工智能研究",
        "机器学习ML算法",
        "深度学习DeepLearning",
        "自然语言处理NLP",
        "计算机视觉CV技术",
        "数据科学DataScience",
        "Python编程语言",
        "TensorFlow框架学习",
        "PyTorch深度学习",
        "Transformer模型",
        "BERT预训练模型",
        "GPT大语言模型",
        "LLM应用开发",
        "RAG检索增强生成"
    };
    
    for (const QString &name : folderNames) {
        query.prepare("INSERT INTO folders (name, parent_id) VALUES (?, NULL)");
        query.addBindValue(name);
        if (!query.exec()) {
            qDebug() << "创建测试文件夹失败:" << query.lastError().text();
        }
    }
}

void TestChineseEnglishSplit::clearTestData()
{
    QSqlQuery query(m_db);
    query.exec("DELETE FROM folders");
}

void TestChineseEnglishSplit::testBasicChineseEnglishSplit()
{
    // 测试基本的中英文拆分：大模型NLP概念 -> 大模型 + NLP + 概念
    QString result = m_databaseApi->searchFolders("大模型NLP概念");
    QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
    QJsonObject obj = doc.object();
    
    QVERIFY(obj["success"].toBool());
    QJsonArray data = obj["data"].toArray();
    QCOMPARE(data.size(), 1); // 应该找到"大模型NLP概念"这个文件夹
    
    if (data.size() > 0) {
        QJsonObject folder = data[0].toObject();
        QCOMPARE(folder["name"].toString(), QString("大模型NLP概念"));
    }
}

void TestChineseEnglishSplit::testComplexMixedKeywords()
{
    // 测试复杂混合：AI人工智能 -> AI + 人工智能
    QString result = m_databaseApi->searchFolders("AI人工智能");
    QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
    QJsonObject obj = doc.object();
    
    QVERIFY(obj["success"].toBool());
    QJsonArray data = obj["data"].toArray();
    QCOMPARE(data.size(), 1); // 应该找到"AI人工智能研究"
}

void TestChineseEnglishSplit::testMultipleMixedKeywords()
{
    // 测试多个混合关键词：机器学习ML,深度学习DeepLearning
    QString result = m_databaseApi->searchFolders("机器学习ML,深度学习DeepLearning");
    QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
    QJsonObject obj = doc.object();
    
    QVERIFY(obj["success"].toBool());
    QJsonArray data = obj["data"].toArray();
    // 这个搜索应该找不到结果，因为没有文件夹同时包含所有这些关键词
    QCOMPARE(data.size(), 0);
}

void TestChineseEnglishSplit::testNumbersInKeywords()
{
    // 测试包含数字的关键词
    QString result = m_databaseApi->searchFolders("GPT3模型");
    QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
    QJsonObject obj = doc.object();
    
    QVERIFY(obj["success"].toBool());
    // 即使没有完全匹配的文件夹，搜索也应该成功执行
}

void TestChineseEnglishSplit::testSpecialCharacters()
{
    // 测试包含特殊字符的关键词
    QString result = m_databaseApi->searchFolders("C++编程");
    QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
    QJsonObject obj = doc.object();
    
    QVERIFY(obj["success"].toBool());
}

void TestChineseEnglishSplit::testEmptyAndWhitespace()
{
    // 测试空字符串和空白字符
    QString result = m_databaseApi->searchFolders("");
    QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
    QJsonObject obj = doc.object();
    
    QVERIFY(!obj["success"].toBool());
    QCOMPARE(obj["message"].toString(), QString("搜索关键字不能为空"));
    
    // 测试只有空白字符
    result = m_databaseApi->searchFolders("   ");
    doc = QJsonDocument::fromJson(result.toUtf8());
    obj = doc.object();
    
    QVERIFY(!obj["success"].toBool());
}

void TestChineseEnglishSplit::testDuplicateKeywords()
{
    // 测试重复关键词的去重功能
    QString result = m_databaseApi->searchFolders("深度学习 深度学习 DeepLearning");
    QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
    QJsonObject obj = doc.object();
    
    QVERIFY(obj["success"].toBool());
    // 应该自动去重，只搜索"深度学习"和"DeepLearning"
}

void TestChineseEnglishSplit::testRealWorldExamples()
{
    // 测试真实世界的例子
    struct TestCase {
        QString input;
        QString expectedMatch;
    };
    
    QList<TestCase> testCases = {
        {"NLP", "自然语言处理NLP"},
        {"TensorFlow", "TensorFlow框架学习"},
        {"PyTorch深度", "PyTorch深度学习"},
        {"BERT", "BERT预训练模型"},
        {"LLM应用", "LLM应用开发"}
    };
    
    for (const TestCase &testCase : testCases) {
        QString result = m_databaseApi->searchFolders(testCase.input);
        QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
        QJsonObject obj = doc.object();
        
        QVERIFY2(obj["success"].toBool(), 
                QString("搜索失败: %1").arg(testCase.input).toUtf8());
        
        QJsonArray data = obj["data"].toArray();
        bool found = false;
        for (int i = 0; i < data.size(); ++i) {
            QJsonObject folder = data[i].toObject();
            if (folder["name"].toString() == testCase.expectedMatch) {
                found = true;
                break;
            }
        }
        
        QVERIFY2(found, 
                QString("未找到期望的文件夹: %1 (搜索: %2)")
                .arg(testCase.expectedMatch, testCase.input).toUtf8());
    }
}

QTEST_MAIN(TestChineseEnglishSplit)
#include "test_chinese_english_split_202507281524.moc"
