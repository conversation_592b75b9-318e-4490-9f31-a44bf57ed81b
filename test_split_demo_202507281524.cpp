#include <QCoreApplication>
#include <QDebug>
#include <QStringList>
#include <QRegularExpression>

// 复制DatabaseApi中的拆分逻辑进行独立测试
QStringList splitChineseEnglishKeywords(const QString &text)
{
    QStringList result;
    
    if (text.trimmed().isEmpty())
    {
        return result;
    }
    
    // 首先按照传统分隔符拆分（空格、逗号、分号）
    QStringList initialSplit = text.split(QRegularExpression("[\\s,;]+"), Qt::SkipEmptyParts);
    
    // 对每个片段进行中英文拆分
    for (const QString &segment : initialSplit)
    {
        QString trimmedSegment = segment.trimmed();
        if (trimmedSegment.isEmpty())
        {
            continue;
        }
        
        // 使用正则表达式匹配连续的中文字符或连续的英文数字字符
        QRegularExpression chineseEnglishPattern(R"(([\u4e00-\u9fff]+|[a-zA-Z0-9]+))");
        QRegularExpressionMatchIterator iterator = chineseEnglishPattern.globalMatch(trimmedSegment);
        
        bool hasMatches = false;
        while (iterator.hasNext())
        {
            QRegularExpressionMatch match = iterator.next();
            QString matchedText = match.captured(1);
            if (!matchedText.isEmpty())
            {
                result.append(matchedText);
                hasMatches = true;
            }
        }
        
        // 如果没有匹配到中英文字符，说明可能包含特殊字符，直接添加原始片段
        if (!hasMatches)
        {
            result.append(trimmedSegment);
        }
    }
    
    // 去重并保持顺序
    QStringList uniqueResult;
    for (const QString &keyword : result)
    {
        if (!uniqueResult.contains(keyword, Qt::CaseInsensitive))
        {
            uniqueResult.append(keyword);
        }
    }
    
    return uniqueResult;
}

void testSplitFunction()
{
    struct TestCase {
        QString input;
        QStringList expected;
        QString description;
    };
    
    QList<TestCase> testCases = {
        {
            "大模型NLP概念",
            {"大模型", "NLP", "概念"},
            "基本中英文混合"
        },
        {
            "AI人工智能研究",
            {"AI", "人工智能研究"},
            "英文开头的混合"
        },
        {
            "机器学习ML算法",
            {"机器学习", "ML", "算法"},
            "中文-英文-中文模式"
        },
        {
            "深度学习DeepLearning框架",
            {"深度学习", "DeepLearning", "框架"},
            "包含长英文单词"
        },
        {
            "Python3编程语言",
            {"Python3", "编程语言"},
            "包含数字的英文"
        },
        {
            "自然语言处理NLP,计算机视觉CV",
            {"自然语言处理", "NLP", "计算机视觉", "CV"},
            "多个混合关键词用逗号分隔"
        },
        {
            "TensorFlow2.0 PyTorch深度学习",
            {"TensorFlow2", "0", "PyTorch", "深度学习"},
            "复杂版本号和混合"
        },
        {
            "BERT预训练模型;GPT大语言模型",
            {"BERT", "预训练模型", "GPT", "大语言模型"},
            "分号分隔的混合关键词"
        },
        {
            "C++编程",
            {"C", "编程"},
            "包含特殊字符"
        },
        {
            "数据科学 DataScience 机器学习",
            {"数据科学", "DataScience", "机器学习"},
            "空格分隔的混合关键词"
        }
    };
    
    qDebug() << "开始测试中英文关键词拆分功能...";
    qDebug() << "==========================================";
    
    int passedTests = 0;
    int totalTests = testCases.size();
    
    for (int i = 0; i < testCases.size(); ++i) {
        const TestCase &testCase = testCases[i];
        QStringList result = splitChineseEnglishKeywords(testCase.input);
        
        qDebug() << QString("测试 %1: %2").arg(i + 1).arg(testCase.description);
        qDebug() << QString("输入: \"%1\"").arg(testCase.input);
        qDebug() << QString("期望: [%1]").arg(testCase.expected.join(", "));
        qDebug() << QString("实际: [%1]").arg(result.join(", "));
        
        bool passed = (result == testCase.expected);
        if (passed) {
            qDebug() << "✅ 通过";
            passedTests++;
        } else {
            qDebug() << "❌ 失败";
        }
        qDebug() << "----------------------------------------";
    }
    
    qDebug() << QString("测试完成: %1/%2 通过").arg(passedTests).arg(totalTests);
    
    if (passedTests == totalTests) {
        qDebug() << "🎉 所有测试都通过了！";
    } else {
        qDebug() << QString("⚠️  有 %1 个测试失败").arg(totalTests - passedTests);
    }
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    testSplitFunction();
    
    return 0;
}
