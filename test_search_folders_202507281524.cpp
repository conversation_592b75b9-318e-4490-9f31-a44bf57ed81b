#include <QtTest/QtTest>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QStandardPaths>
#include <QDir>
#include "qt-src/databaseapi.h"

class TestSearchFolders : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();
    
    void testSingleKeywordSearch();
    void testMultipleKeywordsSearch();
    void testEmptyKeywordSearch();
    void testKeywordWithSpaces();
    void testKeywordWithCommas();
    void testKeywordWithSemicolons();
    void testMixedSeparators();
    void testCaseInsensitiveSearch();

private:
    DatabaseApi *m_databaseApi;
    QSqlDatabase m_db;
    
    void createTestFolders();
    void clearTestData();
};

void TestSearchFolders::initTestCase()
{
    // 设置测试数据库
    QString testDbPath = QStandardPaths::writableLocation(QStandardPaths::TempLocation) + "/test_search_folders.db";
    QFile::remove(testDbPath);
    
    m_db = QSqlDatabase::addDatabase("QSQLITE", "test_connection");
    m_db.setDatabaseName(testDbPath);
    
    if (!m_db.open()) {
        QFAIL("无法打开测试数据库");
    }
    
    m_databaseApi = new DatabaseApi(this);
    m_databaseApi->setDatabasePath(testDbPath);
    m_databaseApi->initializeDatabase();
}

void TestSearchFolders::cleanupTestCase()
{
    delete m_databaseApi;
    m_db.close();
    QSqlDatabase::removeDatabase("test_connection");
}

void TestSearchFolders::init()
{
    createTestFolders();
}

void TestSearchFolders::cleanup()
{
    clearTestData();
}

void TestSearchFolders::createTestFolders()
{
    QSqlQuery query(m_db);
    
    // 创建测试文件夹
    QStringList folderNames = {
        "项目文档",
        "技术资料",
        "会议记录",
        "项目计划",
        "技术文档",
        "个人笔记",
        "工作总结",
        "学习资料",
        "项目管理",
        "技术分享"
    };
    
    for (const QString &name : folderNames) {
        query.prepare("INSERT INTO folders (name, parent_id) VALUES (?, NULL)");
        query.addBindValue(name);
        if (!query.exec()) {
            qDebug() << "创建测试文件夹失败:" << query.lastError().text();
        }
    }
}

void TestSearchFolders::clearTestData()
{
    QSqlQuery query(m_db);
    query.exec("DELETE FROM folders");
}

void TestSearchFolders::testSingleKeywordSearch()
{
    QString result = m_databaseApi->searchFolders("项目");
    QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
    QJsonObject obj = doc.object();
    
    QVERIFY(obj["success"].toBool());
    QJsonArray data = obj["data"].toArray();
    QCOMPARE(data.size(), 3); // 项目文档、项目计划、项目管理
}

void TestSearchFolders::testMultipleKeywordsSearch()
{
    QString result = m_databaseApi->searchFolders("项目 技术");
    QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
    QJsonObject obj = doc.object();
    
    QVERIFY(obj["success"].toBool());
    QJsonArray data = obj["data"].toArray();
    QCOMPARE(data.size(), 0); // 没有同时包含"项目"和"技术"的文件夹名
}

void TestSearchFolders::testEmptyKeywordSearch()
{
    QString result = m_databaseApi->searchFolders("");
    QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
    QJsonObject obj = doc.object();
    
    QVERIFY(!obj["success"].toBool());
    QCOMPARE(obj["message"].toString(), QString("搜索关键字不能为空"));
}

void TestSearchFolders::testKeywordWithSpaces()
{
    QString result = m_databaseApi->searchFolders("项目   文档");
    QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
    QJsonObject obj = doc.object();
    
    QVERIFY(obj["success"].toBool());
    QJsonArray data = obj["data"].toArray();
    QCOMPARE(data.size(), 1); // 项目文档
}

void TestSearchFolders::testKeywordWithCommas()
{
    QString result = m_databaseApi->searchFolders("技术,资料");
    QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
    QJsonObject obj = doc.object();
    
    QVERIFY(obj["success"].toBool());
    QJsonArray data = obj["data"].toArray();
    QCOMPARE(data.size(), 2); // 技术资料、学习资料
}

void TestSearchFolders::testKeywordWithSemicolons()
{
    QString result = m_databaseApi->searchFolders("会议;记录");
    QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
    QJsonObject obj = doc.object();
    
    QVERIFY(obj["success"].toBool());
    QJsonArray data = obj["data"].toArray();
    QCOMPARE(data.size(), 1); // 会议记录
}

void TestSearchFolders::testMixedSeparators()
{
    QString result = m_databaseApi->searchFolders("技术, 文档; 分享");
    QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
    QJsonObject obj = doc.object();
    
    QVERIFY(obj["success"].toBool());
    QJsonArray data = obj["data"].toArray();
    QCOMPARE(data.size(), 1); // 技术分享
}

void TestSearchFolders::testCaseInsensitiveSearch()
{
    // 创建一个英文文件夹用于测试
    QSqlQuery query(m_db);
    query.prepare("INSERT INTO folders (name, parent_id) VALUES (?, NULL)");
    query.addBindValue("Project Documents");
    query.exec();
    
    QString result = m_databaseApi->searchFolders("project documents");
    QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
    QJsonObject obj = doc.object();
    
    QVERIFY(obj["success"].toBool());
    QJsonArray data = obj["data"].toArray();
    QCOMPARE(data.size(), 1); // Project Documents
}

QTEST_MAIN(TestSearchFolders)
#include "test_search_folders_202507281524.moc"
