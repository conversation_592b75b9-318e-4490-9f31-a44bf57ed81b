# 测试多关键词搜索功能的脚本
# 日期: 2025-07-28 15:24

Write-Host "开始测试 searchFolders 多关键词搜索功能..." -ForegroundColor Green

# 检查Qt环境
$qtPath = "C:\Qt\6.9.1\msvc2022_64"
if (-not (Test-Path $qtPath)) {
    Write-Host "错误: 找不到Qt安装路径 $qtPath" -ForegroundColor Red
    exit 1
}

# 设置环境变量
$env:PATH = "$qtPath\bin;$env:PATH"
$env:CMAKE_PREFIX_PATH = $qtPath

# 创建临时构建目录
$buildDir = "build_test_search"
if (Test-Path $buildDir) {
    Remove-Item -Recurse -Force $buildDir
}
New-Item -ItemType Directory -Path $buildDir | Out-Null

try {
    # 进入构建目录
    Push-Location $buildDir
    
    # 创建CMakeLists.txt用于测试
    $cmakeContent = @"
cmake_minimum_required(VERSION 3.16)
project(TestSearchFolders)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(Qt6 REQUIRED COMPONENTS Core Sql Test)

qt6_standard_project_setup()

# 添加测试可执行文件
qt6_add_executable(test_search_folders
    ../test_search_folders_202507281524.cpp
    ../qt-src/databaseapi.cpp
)

target_link_libraries(test_search_folders
    Qt6::Core
    Qt6::Sql
    Qt6::Test
)

target_include_directories(test_search_folders PRIVATE
    ../
)

# 启用测试
enable_testing()
add_test(NAME SearchFoldersTest COMMAND test_search_folders)
"@
    
    Set-Content -Path "CMakeLists.txt" -Value $cmakeContent
    
    Write-Host "配置CMake..." -ForegroundColor Yellow
    cmake .. -G "Visual Studio 17 2022" -A x64
    
    if ($LASTEXITCODE -ne 0) {
        throw "CMake配置失败"
    }
    
    Write-Host "编译测试..." -ForegroundColor Yellow
    cmake --build . --config Debug
    
    if ($LASTEXITCODE -ne 0) {
        throw "编译失败"
    }
    
    Write-Host "运行测试..." -ForegroundColor Yellow
    .\Debug\test_search_folders.exe
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 所有测试通过!" -ForegroundColor Green
    } else {
        Write-Host "❌ 测试失败" -ForegroundColor Red
    }
    
} catch {
    Write-Host "错误: $_" -ForegroundColor Red
    exit 1
} finally {
    Pop-Location
    # 清理构建目录
    if (Test-Path $buildDir) {
        Remove-Item -Recurse -Force $buildDir
    }
}

Write-Host "测试完成" -ForegroundColor Green
