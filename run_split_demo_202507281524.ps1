# 测试中英文关键词拆分功能的演示脚本
# 日期: 2025-07-28 15:24

Write-Host "开始测试中英文关键词拆分功能..." -ForegroundColor Green

# 检查Qt环境
$qtPath = "C:\Qt\6.9.1\msvc2022_64"
if (-not (Test-Path $qtPath)) {
    Write-Host "错误: 找不到Qt安装路径 $qtPath" -ForegroundColor Red
    exit 1
}

# 设置环境变量
$env:PATH = "$qtPath\bin;$env:PATH"
$env:CMAKE_PREFIX_PATH = $qtPath

# 创建临时构建目录
$buildDir = "build_split_demo"
if (Test-Path $buildDir) {
    Remove-Item -Recurse -Force $buildDir
}
New-Item -ItemType Directory -Path $buildDir | Out-Null

try {
    # 进入构建目录
    Push-Location $buildDir
    
    # 创建CMakeLists.txt用于演示
    $cmakeContent = @"
cmake_minimum_required(VERSION 3.16)
project(TestSplitDemo)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(Qt6 REQUIRED COMPONENTS Core)

qt6_standard_project_setup()

# 添加演示可执行文件
qt6_add_executable(test_split_demo
    ../test_split_demo_202507281524.cpp
)

target_link_libraries(test_split_demo
    Qt6::Core
)
"@
    
    Set-Content -Path "CMakeLists.txt" -Value $cmakeContent
    
    Write-Host "配置CMake..." -ForegroundColor Yellow
    cmake .. -G "Visual Studio 17 2022" -A x64
    
    if ($LASTEXITCODE -ne 0) {
        throw "CMake配置失败"
    }
    
    Write-Host "编译演示程序..." -ForegroundColor Yellow
    cmake --build . --config Debug
    
    if ($LASTEXITCODE -ne 0) {
        throw "编译失败"
    }
    
    Write-Host "运行中英文拆分演示..." -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Cyan
    .\Debug\test_split_demo.exe
    Write-Host "========================================" -ForegroundColor Cyan
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 演示程序运行成功!" -ForegroundColor Green
    } else {
        Write-Host "❌ 演示程序运行失败" -ForegroundColor Red
    }
    
} catch {
    Write-Host "错误: $_" -ForegroundColor Red
    exit 1
} finally {
    Pop-Location
    # 清理构建目录
    if (Test-Path $buildDir) {
        Remove-Item -Recurse -Force $buildDir
    }
}

Write-Host "演示完成" -ForegroundColor Green
